import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>R<PERSON>, ArrowLeft, Play } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const TutorialVideosPage = () => {
  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-gray-900">Video Tutorials</h1>
          <Badge variant="outline">All Levels • Various</Badge>
        </div>
        <p className="text-lg text-gray-600">
          Watch comprehensive video guides covering all aspects of Brand Bot Nexus.
        </p>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Play className="mr-2 h-5 w-5" />
              Video Library
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Our video tutorial library covers everything from basic setup to advanced customization techniques.
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between mt-8">
        <Button variant="outline" asChild>
          <Link to="/tutorial/websocket">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous: WebSocket Demo
          </Link>
        </Button>
        <Button asChild>
          <Link to="/tutorial/animations">
            Next: 2D/3D Animations
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default TutorialVideosPage;
