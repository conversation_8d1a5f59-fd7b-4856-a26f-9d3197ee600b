import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, ArrowLeft, LayoutDashboard, Settings, BarChart3 } from "lucide-react";
import { Link } from "react-router-dom";

const TutorialAdminDashboardPage = () => {
  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard Tutorial</h1>
          <Badge variant="outline">Intermediate • 20 min</Badge>
        </div>
        <p className="text-lg text-gray-600">
          Master the admin dashboard features and learn how to manage your chatbot effectively.
        </p>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <LayoutDashboard className="mr-2 h-5 w-5" />
              Dashboard Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              The admin dashboard is your central hub for managing all aspects of your chatbot. Here you can configure settings, monitor performance, and analyze user interactions.
            </p>
            <Button asChild>
              <Link to="/admin/dashboard">
                Open Admin Dashboard
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="mr-2 h-5 w-5" />
              Key Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-semibold mb-2">Configuration</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• AI Model Configuration</li>
                  <li>• Widget Customization</li>
                  <li>• Context Rules Management</li>
                  <li>• Prompt Templates</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Management</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• User Management</li>
                  <li>• API Keys</li>
                  <li>• Web Scraping</li>
                  <li>• Analytics & Reports</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5" />
              Analytics & Monitoring
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Monitor your chatbot's performance with comprehensive analytics and real-time metrics.
            </p>
            <Button asChild variant="outline">
              <Link to="/admin/analytics">
                View Analytics
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between mt-8">
        <Button variant="outline" asChild>
          <Link to="/tutorial/chat-widget">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous: Chat Widget
          </Link>
        </Button>
        <Button asChild>
          <Link to="/tutorial/embedding">
            Next: Embedding Options
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default TutorialAdminDashboardPage;
