import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON><PERSON>, Sparkles } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const TutorialAnimationsPage = () => {
  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-gray-900">2D/3D Animations</h1>
          <Badge variant="outline">Advanced • 25 min</Badge>
        </div>
        <p className="text-lg text-gray-600">
          Add engaging animations to your chatbot for a more interactive user experience.
        </p>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Sparkles className="mr-2 h-5 w-5" />
              Animation Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Learn how to implement smooth animations and transitions to make your chatbot more engaging and visually appealing.
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between mt-8">
        <Button variant="outline" asChild>
          <Link to="/tutorial/videos">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous: Video Tutorials
          </Link>
        </Button>
        <Button asChild>
          <Link to="/tutorial">
            Back to Tutorials
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default TutorialAnimationsPage;
