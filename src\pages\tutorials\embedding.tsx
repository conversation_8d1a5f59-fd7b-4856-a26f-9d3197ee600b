import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, ArrowLeft, Code, ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";

const TutorialEmbeddingPage = () => {
  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-gray-900">Embedding Options</h1>
          <Badge variant="outline">Intermediate • 12 min</Badge>
        </div>
        <p className="text-lg text-gray-600">
          Explore different ways to embed your chatbot into websites and applications.
        </p>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Code className="mr-2 h-5 w-5" />
              Embedding Methods
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-semibold mb-2">Script Tag</h4>
                <p className="text-sm text-gray-600 mb-2">Simple HTML script inclusion</p>
                <div className="bg-gray-100 p-3 rounded text-xs">
                  <code>{`<script src="widget.js"></script>`}</code>
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">React Component</h4>
                <p className="text-sm text-gray-600 mb-2">For React applications</p>
                <div className="bg-gray-100 p-3 rounded text-xs">
                  <code>{`<ChatWidget config={...} />`}</code>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Generate Embed Code</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Use our embed code generator to create the perfect integration for your website.
            </p>
            <Button asChild>
              <Link to="/admin/embed-code">
                Generate Embed Code
                <ExternalLink className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between mt-8">
        <Button variant="outline" asChild>
          <Link to="/tutorial/admin-dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous: Admin Dashboard
          </Link>
        </Button>
        <Button asChild>
          <Link to="/tutorial/websocket">
            Next: WebSocket Demo
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default TutorialEmbeddingPage;
