import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, Circle, ArrowRight, ArrowLeft, Copy, ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";

const TutorialSetupPage = () => {
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const toggleStep = (stepIndex: number) => {
    setCompletedSteps(prev => 
      prev.includes(stepIndex) 
        ? prev.filter(i => i !== stepIndex)
        : [...prev, stepIndex]
    );
  };

  const steps = [
    {
      title: "Create Your Account",
      description: "Sign up for Brand Bot Nexus and verify your email",
      content: (
        <div className="space-y-4">
          <p>Start by creating your account to access all features:</p>
          <ol className="list-decimal list-inside space-y-2 ml-4">
            <li>Click the "Sign Up" button in the top navigation</li>
            <li>Fill in your email, name, and password</li>
            <li>Check your email for verification link</li>
            <li>Click the verification link to activate your account</li>
          </ol>
          <Button asChild>
            <Link to="/auth/register">Sign Up Now</Link>
          </Button>
        </div>
      )
    },
    {
      title: "Configure Your First AI Model",
      description: "Set up an AI model to power your chatbot",
      content: (
        <div className="space-y-4">
          <p>Configure an AI model to handle conversations:</p>
          <ol className="list-decimal list-inside space-y-2 ml-4">
            <li>Navigate to Admin Dashboard → AI Configuration</li>
            <li>Click "Add New Model"</li>
            <li>Choose your preferred provider (OpenAI, Gemini, etc.)</li>
            <li>Enter your API key</li>
            <li>Test the connection</li>
            <li>Save and activate the model</li>
          </ol>
          <Button asChild variant="outline">
            <Link to="/admin/ai-config">Configure AI Model</Link>
          </Button>
        </div>
      )
    },
    {
      title: "Customize Your Widget",
      description: "Design your chat widget to match your brand",
      content: (
        <div className="space-y-4">
          <p>Customize the appearance and behavior of your chat widget:</p>
          <ol className="list-decimal list-inside space-y-2 ml-4">
            <li>Go to Admin Dashboard → Widget Config</li>
            <li>Choose colors that match your brand</li>
            <li>Set your welcome message</li>
            <li>Configure widget position and size</li>
            <li>Preview your changes in real-time</li>
          </ol>
          <Button asChild variant="outline">
            <Link to="/admin/widget-config">Customize Widget</Link>
          </Button>
        </div>
      )
    },
    {
      title: "Set Up Context Rules",
      description: "Define how your chatbot should respond in different scenarios",
      content: (
        <div className="space-y-4">
          <p>Create context rules to guide your chatbot's responses:</p>
          <ol className="list-decimal list-inside space-y-2 ml-4">
            <li>Navigate to Admin Dashboard → Context Rules</li>
            <li>Click "Create New Rule"</li>
            <li>Define trigger keywords or phrases</li>
            <li>Set the appropriate response or action</li>
            <li>Test your rules with the built-in tester</li>
          </ol>
          <Button asChild variant="outline">
            <Link to="/admin/context-rules">Create Context Rules</Link>
          </Button>
        </div>
      )
    },
    {
      title: "Generate Embed Code",
      description: "Get the code to add your chatbot to your website",
      content: (
        <div className="space-y-4">
          <p>Generate and implement the embed code:</p>
          <ol className="list-decimal list-inside space-y-2 ml-4">
            <li>Go to Admin Dashboard → Embed Code</li>
            <li>Choose your integration method (Script tag, React component, etc.)</li>
            <li>Copy the generated code</li>
            <li>Paste it into your website's HTML</li>
            <li>Test the integration</li>
          </ol>
          <div className="bg-gray-100 p-4 rounded-lg">
            <code className="text-sm">
              {`<script src="https://your-domain.com/widget.js"></script>`}
            </code>
            <Button size="sm" variant="ghost" className="ml-2">
              <Copy className="h-4 w-4" />
            </Button>
          </div>
          <Button asChild variant="outline">
            <Link to="/admin/embed-code">Generate Embed Code</Link>
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-gray-900">Setup Guide</h1>
          <Badge variant="outline">Beginner • 10 min</Badge>
        </div>
        <p className="text-lg text-gray-600">
          Follow these steps to set up your Brand Bot Nexus chatbot from scratch.
        </p>
      </div>

      <div className="space-y-6">
        {steps.map((step, index) => (
          <Card key={index} className="relative">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => toggleStep(index)}
                  className="flex-shrink-0"
                >
                  {completedSteps.includes(index) ? (
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  ) : (
                    <Circle className="h-6 w-6 text-gray-400" />
                  )}
                </button>
                <div>
                  <CardTitle className="text-xl">
                    Step {index + 1}: {step.title}
                  </CardTitle>
                  <p className="text-gray-600 mt-1">{step.description}</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="ml-9">
              {step.content}
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-between mt-8">
        <Button variant="outline" asChild>
          <Link to="/tutorial">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Tutorials
          </Link>
        </Button>
        <Button asChild>
          <Link to="/tutorial/chat-widget">
            Next: Chat Widget
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default TutorialSetupPage;
